/* Client Area Specific Styles */

/* Client Area Hero Section */
.client-area-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 120px 0 80px;
}

.client-area-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.client-area-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.client-area-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
}

/* Authentication Container */
.auth-container {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

/* Authentication Tabs */
.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--background-tertiary);
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.auth-tab.active {
    background: var(--secondary-color);
    color: var(--white);
}

/* Form Styles */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 16px;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(192, 165, 213, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
}

.checkmark {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    background: var(--background-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 10px;
    transition: var(--transition);
}

.checkbox-container input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-container input[type="checkbox"]:checked ~ .checkmark {
    background: var(--accent-color);
    border-color: var(--accent-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input[type="checkbox"]:checked ~ .checkmark:after {
    display: block;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 16px;
    width: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(108, 91, 185, 0.3);
}

/* Forgot Password Link */
.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.terms-link {
    color: var(--accent-color);
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Client Dashboard Styles */
.client-dashboard {
    padding: 120px 0 80px;
    min-height: 100vh;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 60px;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
    border-color: var(--accent-color);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.card-action {
    display: inline-flex;
    align-items: center;
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.card-action:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.card-action i {
    margin-left: 8px;
    font-size: 0.9rem;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .client-area-title {
        font-size: 2rem;
    }
    
    .auth-container {
        padding: 30px 20px;
    }
    
    .dashboard-title {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .dashboard-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .client-area-title {
        font-size: 1.8rem;
    }
    
    .dashboard-title {
        font-size: 1.8rem;
    }
}

/* RTL Support for Client Area */
[dir="rtl"] .form-group {
    text-align: right;
}

[dir="rtl"] .checkbox-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .checkmark {
    margin-left: 10px;
    margin-right: 0;
}

[dir="rtl"] .card-action i {
    margin-right: 8px;
    margin-left: 0;
}

[dir="rtl"] .card-action:hover {
    transform: translateX(-5px);
}
