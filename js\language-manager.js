// Language Manager for WIDDX Template
class LanguageManager {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        this.init();
    }
    
    init() {
        this.loadTranslations();
        this.detectLanguage();
        this.setupEventListeners();
        this.applyLanguage(this.currentLanguage);
    }
    
    loadTranslations() {
        this.translations = {
            en: {
                // Navigation
                home: 'Home',
                about: 'About',
                services: 'Services',
                portfolio: 'Portfolio',
                knowledgebase: 'Knowledgebase',
                contact: 'Contact',
                clientArea: 'Client Area',
                
                // Hero Section
                heroTitle: 'Empowering Your Digital World',
                heroSubtitle: 'Hosting. Design. Development. Marketing – All in One Place.',
                getStarted: 'Get Started',
                learnMore: 'Learn More',
                viewServices: 'View Services',
                
                // Services
                servicesTitle: 'What We Do',
                servicesSubtitle: 'Comprehensive digital solutions tailored to your business needs',
                webHosting: 'Web Hosting',
                webHostingDesc: 'Secure, scalable, and lightning-fast hosting for your website.',
                socialMedia: 'Social Media Marketing',
                socialMediaDesc: 'Run powerful campaigns on Facebook, Instagram & more.',
                programming: 'Custom Development',
                programmingDesc: 'Web and software solutions tailored to your business.',
                graphicDesign: 'Graphic Design & Branding',
                graphicDesignDesc: 'Logos, brand identities, video editing, and content creation.',
                
                // Why Choose Us
                whyChooseTitle: 'Why WIDDX?',
                whyChooseSubtitle: 'Three key reasons why businesses choose us for their digital needs',
                
                // Technical Features
                technicalFeaturesTitle: 'Powerful Hosting Technology',
                technicalFeaturesSubtitle: 'Industry-leading technology stack for maximum performance and reliability',
                cpanelTitle: 'cPanel Control Panel',
                cpanelDesc: 'Industry-standard control panel for easy website management and hosting features access.',
                cloudlinuxTitle: 'CloudLinux OS',
                cloudlinuxDesc: 'Isolated environment with balanced CPU, RAM, and disk I/O limits for optimal performance.',
                litespeedTitle: 'LiteSpeed Web Server',
                litespeedDesc: 'Ultra-fast web server technology that delivers up to 3x faster loading speeds.',
                sslTitle: 'Free SSL Certificates',
                sslDesc: 'Automatic SSL installation and renewal for secure HTTPS connections on all websites.',
                
                // Statistics
                statisticsTitle: 'Trusted by Thousands',
                statisticsSubtitle: 'Our numbers speak for themselves - join thousands of satisfied customers worldwide',
                happyCustomers: 'Happy Customers',
                uptimeGuarantee: 'Uptime Guarantee',
                supportAvailable: 'Support Available',
                countriesServed: 'Countries Served',
                
                // Client Area
                clientPortal: 'Client Portal',
                clientPortalSubtitle: 'Access your services, manage billing, and get support',
                signIn: 'Sign In',
                register: 'Register',
                emailAddress: 'Email Address',
                password: 'Password',
                rememberMe: 'Remember me',
                forgotPassword: 'Forgot your password?',
                firstName: 'First Name',
                lastName: 'Last Name',
                confirmPassword: 'Confirm Password',
                agreeTerms: 'I agree to the Terms of Service',
                createAccount: 'Create Account',
                
                // Common
                loading: 'Loading...',
                submit: 'Submit',
                cancel: 'Cancel',
                close: 'Close',
                save: 'Save',
                edit: 'Edit',
                delete: 'Delete',
                confirm: 'Confirm',
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Information'
            },
            ar: {
                // Navigation
                home: 'الرئيسية',
                about: 'من نحن',
                services: 'الخدمات',
                portfolio: 'أعمالنا',
                knowledgebase: 'قاعدة المعرفة',
                contact: 'اتصل بنا',
                clientArea: 'منطقة العملاء',
                
                // Hero Section
                heroTitle: 'نمكن عالمك الرقمي',
                heroSubtitle: 'استضافة. تصميم. تطوير. تسويق - كل شيء في مكان واحد.',
                getStarted: 'ابدأ الآن',
                learnMore: 'اعرف المزيد',
                viewServices: 'عرض الخدمات',
                
                // Services
                servicesTitle: 'ما نقدمه',
                servicesSubtitle: 'حلول رقمية شاملة مصممة خصيصاً لاحتياجات عملك',
                webHosting: 'استضافة المواقع',
                webHostingDesc: 'استضافة آمنة وقابلة للتوسع وسريعة البرق لموقعك الإلكتروني.',
                socialMedia: 'التسويق عبر وسائل التواصل',
                socialMediaDesc: 'تشغيل حملات قوية على فيسبوك وإنستغرام والمزيد.',
                programming: 'التطوير المخصص',
                programmingDesc: 'حلول الويب والبرمجيات المصممة خصيصاً لعملك.',
                graphicDesign: 'التصميم الجرافيكي والهوية',
                graphicDesignDesc: 'الشعارات وهويات العلامات التجارية وتحرير الفيديو وإنشاء المحتوى.',
                
                // Why Choose Us
                whyChooseTitle: 'لماذا WIDDX؟',
                whyChooseSubtitle: 'ثلاثة أسباب رئيسية تجعل الشركات تختارنا لاحتياجاتها الرقمية',
                
                // Technical Features
                technicalFeaturesTitle: 'تقنية استضافة قوية',
                technicalFeaturesSubtitle: 'مجموعة تقنيات رائدة في الصناعة لأقصى أداء وموثوقية',
                cpanelTitle: 'لوحة تحكم cPanel',
                cpanelDesc: 'لوحة تحكم معيارية في الصناعة لإدارة سهلة للموقع والوصول لميزات الاستضافة.',
                cloudlinuxTitle: 'نظام CloudLinux',
                cloudlinuxDesc: 'بيئة معزولة مع حدود متوازنة للمعالج والذاكرة وإدخال/إخراج القرص للأداء الأمثل.',
                litespeedTitle: 'خادم الويب LiteSpeed',
                litespeedDesc: 'تقنية خادم ويب فائقة السرعة توفر سرعات تحميل أسرع بـ 3 مرات.',
                sslTitle: 'شهادات SSL مجانية',
                sslDesc: 'تثبيت وتجديد تلقائي لـ SSL لاتصالات HTTPS آمنة على جميع المواقع.',
                
                // Statistics
                statisticsTitle: 'موثوق من الآلاف',
                statisticsSubtitle: 'أرقامنا تتحدث عن نفسها - انضم لآلاف العملاء الراضين حول العالم',
                happyCustomers: 'عملاء سعداء',
                uptimeGuarantee: 'ضمان وقت التشغيل',
                supportAvailable: 'الدعم متاح',
                countriesServed: 'دولة نخدمها',
                
                // Client Area
                clientPortal: 'بوابة العملاء',
                clientPortalSubtitle: 'الوصول لخدماتك وإدارة الفواتير والحصول على الدعم',
                signIn: 'تسجيل الدخول',
                register: 'إنشاء حساب',
                emailAddress: 'عنوان البريد الإلكتروني',
                password: 'كلمة المرور',
                rememberMe: 'تذكرني',
                forgotPassword: 'نسيت كلمة المرور؟',
                firstName: 'الاسم الأول',
                lastName: 'اسم العائلة',
                confirmPassword: 'تأكيد كلمة المرور',
                agreeTerms: 'أوافق على شروط الخدمة',
                createAccount: 'إنشاء حساب',
                
                // Common
                loading: 'جاري التحميل...',
                submit: 'إرسال',
                cancel: 'إلغاء',
                close: 'إغلاق',
                save: 'حفظ',
                edit: 'تعديل',
                delete: 'حذف',
                confirm: 'تأكيد',
                success: 'نجح',
                error: 'خطأ',
                warning: 'تحذير',
                info: 'معلومات'
            }
        };
    }
    
    detectLanguage() {
        // Check localStorage first
        const savedLang = localStorage.getItem('widdx-language');
        if (savedLang && this.translations[savedLang]) {
            this.currentLanguage = savedLang;
            return;
        }
        
        // Check browser language
        const browserLang = navigator.language.split('-')[0];
        if (this.translations[browserLang]) {
            this.currentLanguage = browserLang;
            return;
        }
        
        // Default to English
        this.currentLanguage = 'en';
    }
    
    setupEventListeners() {
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
        
        // Listen for manual language changes
        document.addEventListener('languageChange', (e) => {
            this.setLanguage(e.detail.language);
        });
    }
    
    toggleLanguage() {
        const newLang = this.currentLanguage === 'en' ? 'ar' : 'en';
        this.setLanguage(newLang);
    }
    
    setLanguage(language) {
        if (!this.translations[language]) {
            console.warn(`Language ${language} not supported`);
            return;
        }
        
        this.currentLanguage = language;
        localStorage.setItem('widdx-language', language);
        this.applyLanguage(language);
        
        // Dispatch event for other components
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: language }
        }));
    }
    
    applyLanguage(language) {
        const html = document.documentElement;
        const languageText = document.getElementById('languageText');
        
        // Set HTML attributes
        html.setAttribute('lang', language);
        html.setAttribute('dir', this.isRTL(language) ? 'rtl' : 'ltr');
        
        // Update language toggle text
        if (languageText) {
            languageText.textContent = language === 'en' ? 'عربي' : 'English';
        }
        
        // Update all translatable elements
        this.updateTranslations(language);
        
        // Apply RTL/LTR specific styles
        this.applyDirectionStyles(language);
    }
    
    updateTranslations(language) {
        const elements = document.querySelectorAll('[data-translate]');
        const translations = this.translations[language];
        
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            if (translations[key]) {
                if (element.tagName === 'INPUT' && element.type === 'submit') {
                    element.value = translations[key];
                } else if (element.hasAttribute('placeholder')) {
                    element.placeholder = translations[key];
                } else {
                    element.textContent = translations[key];
                }
            }
        });
    }
    
    applyDirectionStyles(language) {
        const body = document.body;
        
        if (this.isRTL(language)) {
            body.classList.add('rtl');
            body.classList.remove('ltr');
        } else {
            body.classList.add('ltr');
            body.classList.remove('rtl');
        }
    }
    
    isRTL(language) {
        return this.rtlLanguages.includes(language);
    }
    
    translate(key, language = null) {
        const lang = language || this.currentLanguage;
        return this.translations[lang] && this.translations[lang][key] 
            ? this.translations[lang][key] 
            : key;
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    getSupportedLanguages() {
        return Object.keys(this.translations);
    }
    
    addTranslation(language, key, value) {
        if (!this.translations[language]) {
            this.translations[language] = {};
        }
        this.translations[language][key] = value;
    }
    
    addLanguage(language, translations) {
        this.translations[language] = translations;
    }
}

// Initialize Language Manager
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageManager;
}
