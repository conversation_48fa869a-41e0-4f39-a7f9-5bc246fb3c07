// Modern Background Animation System
class ModernBackground {
    constructor() {
        this.container = null;
        this.isInitialized = false;
        this.animationFrameId = null;
        this.spheres = [];
        this.particles = [];
        this.mousePosition = { x: 0, y: 0 };
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        this.init();
    }
    
    init() {
        // Create background container if it doesn't exist
        this.createBackgroundContainer();
        
        // Initialize components
        this.createGradientLayers();
        this.createGradientSpheres();
        this.createWaveElement();
        this.createParticleSystem();
        this.createNoiseTexture();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Start animation loop if motion is allowed
        if (!this.isReducedMotion) {
            this.startAnimationLoop();
        }
        
        this.isInitialized = true;
    }
    
    createBackgroundContainer() {
        this.container = document.querySelector('.modern-gradient-background');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'modern-gradient-background';
            document.body.appendChild(this.container);
        }
    }
    
    createGradientLayers() {
        for (let i = 1; i <= 3; i++) {
            const layer = document.createElement('div');
            layer.className = `gradient-layer-${i}`;
            this.container.appendChild(layer);
        }
    }
    
    createGradientSpheres() {
        for (let i = 1; i <= 4; i++) {
            const sphere = document.createElement('div');
            sphere.className = `gradient-sphere gradient-sphere-${i}`;
            this.container.appendChild(sphere);
            
            this.spheres.push({
                element: sphere,
                baseX: Math.random() * 100,
                baseY: Math.random() * 100,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                size: 150 + Math.random() * 150
            });
        }
    }
    
    createWaveElement() {
        const wave = document.createElement('div');
        wave.className = 'wave-element';
        this.container.appendChild(wave);
    }
    
    createParticleSystem() {
        const particleSystem = document.createElement('div');
        particleSystem.className = 'particle-system';
        this.container.appendChild(particleSystem);
        
        // Create individual particles for better control
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 3 + 1}px;
                height: ${Math.random() * 3 + 1}px;
                background: rgba(192, 165, 213, ${Math.random() * 0.5 + 0.1});
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                pointer-events: none;
            `;
            
            this.particles.push({
                element: particle,
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                opacity: Math.random() * 0.5 + 0.1
            });
            
            particleSystem.appendChild(particle);
        }
    }
    
    createNoiseTexture() {
        const noise = document.createElement('div');
        noise.className = 'noise-texture';
        this.container.appendChild(noise);
    }
    
    setupEventListeners() {
        // Mouse movement for interactive effects
        document.addEventListener('mousemove', (e) => {
            this.mousePosition.x = e.clientX / window.innerWidth;
            this.mousePosition.y = e.clientY / window.innerHeight;
        });
        
        // Theme change listener
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    this.updateThemeColors();
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
        
        // Resize listener
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Reduced motion preference change
        window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
            this.isReducedMotion = e.matches;
            if (this.isReducedMotion) {
                this.stopAnimationLoop();
            } else {
                this.startAnimationLoop();
            }
        });
    }
    
    startAnimationLoop() {
        if (this.isReducedMotion) return;
        
        const animate = (timestamp) => {
            this.updateSpheres(timestamp);
            this.updateParticles(timestamp);
            this.updateInteractiveEffects();
            
            this.animationFrameId = requestAnimationFrame(animate);
        };
        
        this.animationFrameId = requestAnimationFrame(animate);
    }
    
    stopAnimationLoop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }
    
    updateSpheres(timestamp) {
        this.spheres.forEach((sphere, index) => {
            const time = timestamp * 0.001;
            const mouseInfluence = 0.1;
            
            // Base movement
            sphere.baseX += sphere.speedX;
            sphere.baseY += sphere.speedY;
            
            // Mouse interaction
            const mouseX = this.mousePosition.x * mouseInfluence;
            const mouseY = this.mousePosition.y * mouseInfluence;
            
            // Boundary checking
            if (sphere.baseX > 100 || sphere.baseX < 0) sphere.speedX *= -1;
            if (sphere.baseY > 100 || sphere.baseY < 0) sphere.speedY *= -1;
            
            // Apply transformations
            const x = sphere.baseX + Math.sin(time + index) * 2 + mouseX * 10;
            const y = sphere.baseY + Math.cos(time + index) * 2 + mouseY * 10;
            
            sphere.element.style.transform = `translate(${x}px, ${y}px)`;
        });
    }
    
    updateParticles(timestamp) {
        this.particles.forEach((particle) => {
            particle.x += particle.speedX;
            particle.y += particle.speedY;
            
            // Wrap around screen
            if (particle.x > window.innerWidth) particle.x = 0;
            if (particle.x < 0) particle.x = window.innerWidth;
            if (particle.y > window.innerHeight) particle.y = 0;
            if (particle.y < 0) particle.y = window.innerHeight;
            
            // Mouse interaction
            const mouseDistance = Math.sqrt(
                Math.pow(particle.x - this.mousePosition.x * window.innerWidth, 2) +
                Math.pow(particle.y - this.mousePosition.y * window.innerHeight, 2)
            );
            
            const maxDistance = 100;
            if (mouseDistance < maxDistance) {
                const force = (maxDistance - mouseDistance) / maxDistance;
                particle.opacity = Math.min(1, particle.opacity + force * 0.02);
            } else {
                particle.opacity = Math.max(0.1, particle.opacity - 0.005);
            }
            
            particle.element.style.left = particle.x + 'px';
            particle.element.style.top = particle.y + 'px';
            particle.element.style.opacity = particle.opacity;
        });
    }
    
    updateInteractiveEffects() {
        // Update gradient layers based on mouse position
        const layers = this.container.querySelectorAll('[class^="gradient-layer-"]');
        layers.forEach((layer, index) => {
            const intensity = (this.mousePosition.x + this.mousePosition.y) / 2;
            const offset = intensity * 20;
            layer.style.transform = `translate(${offset * (index + 1)}px, ${offset * (index + 1)}px)`;
        });
    }
    
    updateThemeColors() {
        // Update particle colors based on theme
        const isDark = document.body.getAttribute('data-theme') === 'dark';
        const baseColor = isDark ? '192, 165, 213' : '139, 91, 185';
        
        this.particles.forEach((particle) => {
            particle.element.style.background = `rgba(${baseColor}, ${particle.opacity})`;
        });
    }
    
    handleResize() {
        // Recalculate particle positions on resize
        this.particles.forEach((particle) => {
            if (particle.x > window.innerWidth) particle.x = window.innerWidth;
            if (particle.y > window.innerHeight) particle.y = window.innerHeight;
        });
    }
    
    destroy() {
        this.stopAnimationLoop();
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        this.isInitialized = false;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not on mobile for performance
    const isMobile = window.innerWidth <= 768;
    const hasReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (!isMobile || !hasReducedMotion) {
        window.modernBackground = new ModernBackground();
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.modernBackground) {
        window.modernBackground.destroy();
    }
});
