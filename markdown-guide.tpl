<h4>{lang key='markdown.emphasis'}</h4>
<pre>
**<strong>{lang key='markdown.bold'}</strong>**
*<em>{lang key='markdown.italics'}</em>*</pre>

<h4>{lang key='markdown.headers'}</h4>
<pre>
# {lang key='markdown.bigHeader'}
## {lang key='markdown.mediumHeader'}
### {lang key='markdown.smallHeader'}
#### {lang key='markdown.tinyHeader'}</pre>

<h4>{lang key='markdown.lists'}</h4>
<pre>
* {lang key='markdown.genericListItem'}
* {lang key='markdown.genericListItem'}
* {lang key='markdown.genericListItem'}

1. {lang key='markdown.numberedListItem'}
2. {lang key='markdown.numberedListItem'}
3. {lang key='markdown.numberedListItem'}</pre>

<h4>{lang key='markdown.links'}</h4>
<pre>[{lang key='markdown.textToDisplay'}]({lang key='markdown.exampleLink'})</pre>

<h4>{lang key='markdown.quotes'}</h4>
<pre>
> {lang key='markdown.thisIsAQuote'}
> {lang key='markdown.quoteMultipleLines'}</pre>

<h4>{lang key='markdown.tables'}</h4>
<pre>
| {lang key='markdown.columnOne'} | {lang key='markdown.columnTwo'} | {lang key='markdown.columnThree'} |
| -------- | -------- | -------- |
| {lang key='markdown.john'}     | {lang key='markdown.doe'}      | {lang key='markdown.male'}     |
| {lang key='markdown.mary'}     | {lang key='markdown.smith'}    | {lang key='markdown.female'}   |

<em>{lang key='markdown.withoutAligning'}</em>

| {lang key='markdown.columnOne'} | {lang key='markdown.columnTwo'} | {lang key='markdown.columnThree'} |
| -------- | -------- | -------- |
| {lang key='markdown.john'} | {lang key='markdown.doe'} | {lang key='markdown.male'} |
| {lang key='markdown.mary'} | {lang key='markdown.smith'} | {lang key='markdown.female'} |</pre>

<h4>{lang key='markdown.displayingCode'}</h4>
<pre>
`var example = "hello!";`

<em>{lang key='markdown.spanningMultipleLines'}</em>

```
var example = "hello!";
alert(example);
```</pre>
