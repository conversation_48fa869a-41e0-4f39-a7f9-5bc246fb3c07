/* Critical CSS for Above-the-fold Content */
/* This CSS should be inlined in the head for optimal performance */

/* CSS Variables */
:root {
    --primary-color: #010815;
    --secondary-color: #6c5bb9;
    --accent-color: #c0a5d5;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --background-primary: #010815;
    --background-secondary: #0f1419;
    --background-tertiary: rgba(255, 255, 255, 0.05);
    --border-color: rgba(192, 165, 213, 0.1);
    --card-background: rgba(255, 255, 255, 0.05);
    --navbar-background: rgba(1, 8, 21, 0.95);
    --footer-background: #000510;
    --white: #ffffff;
    --gray-light: #f8f9fa;
    --gray-dark: #2c3e50;
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.1);
    --container-max-width: 1200px;
    --section-padding: 80px 0;
}

/* Light Theme */
[data-theme="light"] {
    --primary-color: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: rgba(26, 26, 26, 0.8);
    --text-muted: rgba(26, 26, 26, 0.6);
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: rgba(108, 91, 185, 0.05);
    --border-color: rgba(108, 91, 185, 0.2);
    --card-background: rgba(108, 91, 185, 0.05);
    --navbar-background: rgba(255, 255, 255, 0.95);
    --footer-background: #f1f3f4;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.15);
}

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 14px 28px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-secondary:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Preloader */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.preloader-content {
    text-align: center;
}

.logo-animation h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 3px;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(192, 165, 213, 0.2);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: var(--navbar-background);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.navbar.scrolled {
    padding: 10px 0;
    background: var(--navbar-background);
    box-shadow: var(--box-shadow);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.client-area {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    padding: 8px 16px !important;
    border-radius: 20px;
    color: var(--white) !important;
}

.client-area::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

.theme-toggle {
    background: transparent;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    cursor: pointer;
    transition: var(--transition);
    margin-left: 15px;
}

.theme-toggle:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1rem;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--background-primary);
    transition: background 0.3s ease;
}

.hero-content {
    opacity: 1;
    z-index: 2;
    position: relative;
}

.hero-content > * {
    opacity: 1;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(108, 91, 185, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
    opacity: 0.7;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.title-line {
    display: block;
}

.highlight {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 0 50px;
    }
    
    .hero-content {
        margin-bottom: 2rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
}
