/* Modern Gradient Background Styles */
.modern-gradient-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    background: var(--background-primary);
    transition: all 0.3s ease;
}

.gradient-layer-1,
.gradient-layer-2,
.gradient-layer-3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.gradient-layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(15, 20, 25, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(192, 165, 213, 0.1) 0%, transparent 50%);
    animation: gradientShift1 20s ease-in-out infinite;
}

.gradient-layer-2 {
    background: radial-gradient(circle at 60% 20%, rgba(108, 91, 185, 0.08) 0%, transparent 40%),
                radial-gradient(circle at 30% 60%, rgba(192, 165, 213, 0.06) 0%, transparent 45%);
    animation: gradientShift2 25s ease-in-out infinite reverse;
}

.gradient-layer-3 {
    background: radial-gradient(circle at 80% 40%, rgba(15, 20, 25, 0.7) 0%, transparent 35%),
                radial-gradient(circle at 10% 80%, rgba(108, 91, 185, 0.12) 0%, transparent 50%);
    animation: gradientShift3 30s ease-in-out infinite;
}

/* Gradient Spheres */
.gradient-sphere {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
}

.gradient-sphere-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(108, 91, 185, 0.3) 0%, transparent 70%);
    top: 10%;
    left: 15%;
    animation: floatSphere1 15s ease-in-out infinite;
}

.gradient-sphere-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(192, 165, 213, 0.25) 0%, transparent 70%);
    top: 60%;
    right: 20%;
    animation: floatSphere2 18s ease-in-out infinite;
}

.gradient-sphere-3 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(108, 91, 185, 0.2) 0%, transparent 70%);
    bottom: 20%;
    left: 25%;
    animation: floatSphere3 22s ease-in-out infinite;
}

.gradient-sphere-4 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(192, 165, 213, 0.15) 0%, transparent 70%);
    top: 30%;
    right: 10%;
    animation: floatSphere4 20s ease-in-out infinite;
}

/* Wave Element */
.wave-element {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(180deg, transparent 0%, rgba(108, 91, 185, 0.05) 100%);
    clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
    animation: waveMotion 12s ease-in-out infinite;
}

/* Particle System */
.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(192, 165, 213, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(108, 91, 185, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(192, 165, 213, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(108, 91, 185, 0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(192, 165, 213, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 25s linear infinite;
}

/* Noise Texture */
.noise-texture {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.02;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Animations */
@keyframes gradientShift1 {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
    }
    25% {
        transform: translateX(-10px) translateY(-15px) scale(1.05);
    }
    50% {
        transform: translateX(15px) translateY(-10px) scale(0.95);
    }
    75% {
        transform: translateX(-5px) translateY(20px) scale(1.02);
    }
}

@keyframes gradientShift2 {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    33% {
        transform: translateX(20px) translateY(-25px) rotate(1deg);
    }
    66% {
        transform: translateX(-15px) translateY(15px) rotate(-1deg);
    }
}

@keyframes gradientShift3 {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1) rotate(0deg);
    }
    50% {
        transform: translateX(-20px) translateY(-30px) scale(1.1) rotate(2deg);
    }
}

@keyframes floatSphere1 {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(30px) translateY(-20px);
    }
    50% {
        transform: translateX(-20px) translateY(-40px);
    }
    75% {
        transform: translateX(-10px) translateY(20px);
    }
}

@keyframes floatSphere2 {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    33% {
        transform: translateX(-25px) translateY(30px);
    }
    66% {
        transform: translateX(40px) translateY(-15px);
    }
}

@keyframes floatSphere3 {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    50% {
        transform: translateX(35px) translateY(-25px);
    }
}

@keyframes floatSphere4 {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(-30px) translateY(20px);
    }
    50% {
        transform: translateX(20px) translateY(-30px);
    }
    75% {
        transform: translateX(10px) translateY(15px);
    }
}

@keyframes waveMotion {
    0%, 100% {
        clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
    }
    50% {
        clip-path: polygon(0 80%, 100% 50%, 100% 100%, 0% 100%);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-100px);
    }
}

/* Light Theme Adjustments */
[data-theme="light"] .gradient-layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(248, 249, 250, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(108, 91, 185, 0.08) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(139, 91, 185, 0.06) 0%, transparent 50%);
}

[data-theme="light"] .gradient-layer-2 {
    background: radial-gradient(circle at 60% 20%, rgba(108, 91, 185, 0.04) 0%, transparent 40%),
                radial-gradient(circle at 30% 60%, rgba(139, 91, 185, 0.03) 0%, transparent 45%);
}

[data-theme="light"] .gradient-layer-3 {
    background: radial-gradient(circle at 80% 40%, rgba(248, 249, 250, 0.8) 0%, transparent 35%),
                radial-gradient(circle at 10% 80%, rgba(108, 91, 185, 0.06) 0%, transparent 50%);
}

[data-theme="light"] .gradient-sphere-1 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.15) 0%, transparent 70%);
}

[data-theme="light"] .gradient-sphere-2 {
    background: radial-gradient(circle, rgba(139, 91, 185, 0.12) 0%, transparent 70%);
}

[data-theme="light"] .gradient-sphere-3 {
    background: radial-gradient(circle, rgba(108, 91, 185, 0.1) 0%, transparent 70%);
}

[data-theme="light"] .gradient-sphere-4 {
    background: radial-gradient(circle, rgba(139, 91, 185, 0.08) 0%, transparent 70%);
}

[data-theme="light"] .wave-element {
    background: linear-gradient(180deg, transparent 0%, rgba(108, 91, 185, 0.03) 100%);
}

[data-theme="light"] .particle-system {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(139, 91, 185, 0.15), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(108, 91, 185, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(139, 91, 185, 0.2), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(108, 91, 185, 0.15), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(139, 91, 185, 0.1), transparent);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .gradient-sphere-1,
    .gradient-sphere-2,
    .gradient-sphere-3,
    .gradient-sphere-4 {
        width: 150px;
        height: 150px;
    }
    
    .wave-element {
        height: 100px;
    }
    
    .particle-system {
        background-size: 100px 50px;
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    .gradient-layer-1,
    .gradient-layer-2,
    .gradient-layer-3,
    .gradient-sphere-1,
    .gradient-sphere-2,
    .gradient-sphere-3,
    .gradient-sphere-4,
    .wave-element,
    .particle-system {
        animation: none;
    }
}
