// Client Area JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeClientArea();
});

function initializeClientArea() {
    initializeAuthTabs();
    initializeFormValidation();
    initializePasswordToggle();
    initializeDashboardFeatures();
}

// Authentication Tabs
function initializeAuthTabs() {
    const loginTab = document.getElementById('loginTab');
    const registerTab = document.getElementById('registerTab');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (loginTab && registerTab && loginForm && registerForm) {
        loginTab.addEventListener('click', function() {
            switchTab('login');
        });
        
        registerTab.addEventListener('click', function() {
            switchTab('register');
        });
    }
}

function switchTab(tab) {
    const loginTab = document.getElementById('loginTab');
    const registerTab = document.getElementById('registerTab');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (tab === 'login') {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
    } else {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        registerForm.style.display = 'block';
        loginForm.style.display = 'none';
    }
}

// Form Validation
function initializeFormValidation() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateLoginForm();
        });
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateRegisterForm();
        });
    }
    
    // Real-time validation
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

function validateLoginForm() {
    const email = document.getElementById('loginEmail');
    const password = document.getElementById('loginPassword');
    let isValid = true;
    
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    if (password.value.length < 6) {
        showFieldError(password, 'Password must be at least 6 characters');
        isValid = false;
    }
    
    if (isValid) {
        // Simulate login process
        showLoadingState(document.querySelector('#loginForm .btn'));
        setTimeout(() => {
            // Redirect to dashboard or show success
            window.location.href = 'client-dashboard.html';
        }, 2000);
    }
}

function validateRegisterForm() {
    const firstName = document.getElementById('registerFirstName');
    const lastName = document.getElementById('registerLastName');
    const email = document.getElementById('registerEmail');
    const password = document.getElementById('registerPassword');
    const confirmPassword = document.getElementById('registerConfirmPassword');
    const agreeTerms = document.getElementById('agreeTerms');
    let isValid = true;
    
    if (firstName.value.trim().length < 2) {
        showFieldError(firstName, 'First name must be at least 2 characters');
        isValid = false;
    }
    
    if (lastName.value.trim().length < 2) {
        showFieldError(lastName, 'Last name must be at least 2 characters');
        isValid = false;
    }
    
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    if (password.value.length < 8) {
        showFieldError(password, 'Password must be at least 8 characters');
        isValid = false;
    }
    
    if (password.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Passwords do not match');
        isValid = false;
    }
    
    if (!agreeTerms.checked) {
        showFieldError(agreeTerms, 'You must agree to the terms of service');
        isValid = false;
    }
    
    if (isValid) {
        // Simulate registration process
        showLoadingState(document.querySelector('#registerForm .btn'));
        setTimeout(() => {
            showSuccessMessage('Account created successfully! Please check your email to verify your account.');
        }, 2000);
    }
}

function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldId = field.id;
    
    clearFieldError(field);
    
    switch (fieldType) {
        case 'email':
            if (!validateEmail(value)) {
                showFieldError(field, 'Please enter a valid email address');
            }
            break;
        case 'password':
            if (value.length < 6) {
                showFieldError(field, 'Password must be at least 6 characters');
            }
            break;
        case 'text':
            if (value.length < 2) {
                showFieldError(field, 'This field must be at least 2 characters');
            }
            break;
    }
    
    // Special validation for confirm password
    if (fieldId === 'registerConfirmPassword') {
        const password = document.getElementById('registerPassword');
        if (value !== password.value) {
            showFieldError(field, 'Passwords do not match');
        }
    }
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.cssText = `
        color: #ff6b6b;
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    `;
    
    field.parentNode.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function showLoadingState(button) {
    const originalText = button.textContent;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    
    // Reset after 3 seconds
    setTimeout(() => {
        button.disabled = false;
        button.textContent = originalText;
    }, 3000);
}

function showSuccessMessage(message) {
    const successElement = document.createElement('div');
    successElement.className = 'success-message';
    successElement.textContent = message;
    successElement.style.cssText = `
        background: rgba(76, 175, 80, 0.1);
        border: 1px solid #4caf50;
        color: #4caf50;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
    `;
    
    const authContainer = document.querySelector('.auth-container');
    authContainer.insertBefore(successElement, authContainer.firstChild);
    
    // Remove after 5 seconds
    setTimeout(() => {
        successElement.remove();
    }, 5000);
}

// Password Toggle
function initializePasswordToggle() {
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    passwordFields.forEach(field => {
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'password-toggle';
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.style.cssText = `
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
        `;
        
        // Make parent relative
        field.parentNode.style.position = 'relative';
        field.style.paddingRight = '40px';
        field.parentNode.appendChild(toggleButton);
        
        toggleButton.addEventListener('click', function() {
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });
    });
}

// Dashboard Features
function initializeDashboardFeatures() {
    // Animate dashboard cards on load
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Animate statistics
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'scale(1)';
        }, index * 50);
    });
}

// Forgot Password Modal
function showForgotPasswordModal() {
    const modal = document.createElement('div');
    modal.className = 'forgot-password-modal';
    modal.innerHTML = `
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Reset Password</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Enter your email address and we'll send you a link to reset your password.</p>
                    <form id="forgotPasswordForm">
                        <div class="form-group">
                            <label for="resetEmail" class="form-label">Email Address</label>
                            <input type="email" id="resetEmail" class="form-input" required>
                        </div>
                        <button type="submit" class="btn">Send Reset Link</button>
                    </form>
                </div>
            </div>
        </div>
    `;
    
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    closeBtn.addEventListener('click', () => modal.remove());
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) modal.remove();
    });
    
    // Form submission
    const form = modal.querySelector('#forgotPasswordForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('resetEmail').value;
        
        if (validateEmail(email)) {
            showLoadingState(this.querySelector('.btn'));
            setTimeout(() => {
                modal.remove();
                showSuccessMessage('Password reset link sent to your email!');
            }, 2000);
        }
    });
}

// Initialize forgot password link
document.addEventListener('DOMContentLoaded', function() {
    const forgotPasswordLink = document.getElementById('forgotPasswordLink');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            showForgotPasswordModal();
        });
    }
});
