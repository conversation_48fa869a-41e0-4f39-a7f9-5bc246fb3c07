/* WIDDX Custom Design - Main Styles */
/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: transparent; /* Let the modern background show through */
    overflow-x: hidden;
    transition: color 0.3s ease;
    position: relative;
}

/* ===== CSS VARIABLES ===== */
:root {
    /* Dark Theme (Default) */
    --primary-color: #010815;
    --secondary-color: #6c5bb9;
    --accent-color: #c0a5d5;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --background-primary: #010815;
    --background-secondary: #0f1419;
    --background-tertiary: rgba(255, 255, 255, 0.05);
    --border-color: rgba(192, 165, 213, 0.1);
    --card-background: rgba(255, 255, 255, 0.05);
    --navbar-background: rgba(1, 8, 21, 0.95);
    --footer-background: #000510;

    /* Common Variables */
    --white: #ffffff;
    --gray-light: #f8f9fa;
    --gray-dark: #2c3e50;
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.1);
    --container-max-width: 1200px;
    --section-padding: 80px 0;
}

/* Dark Theme Explicit */
[data-theme="dark"] {
    --primary-color: #010815;
    --secondary-color: #6c5bb9;
    --accent-color: #c0a5d5;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --background-primary: #010815;
    --background-secondary: #0f1419;
    --background-tertiary: rgba(255, 255, 255, 0.05);
    --border-color: rgba(192, 165, 213, 0.1);
    --card-background: rgba(255, 255, 255, 0.05);
    --navbar-background: rgba(1, 8, 21, 0.95);
    --footer-background: #000510;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.1);
}

/* Light Theme */
[data-theme="light"] {
    --primary-color: #ffffff;
    --secondary-color: #6c5bb9;
    --accent-color: #8b5bb9;
    --text-primary: #1a1a1a;
    --text-secondary: rgba(26, 26, 26, 0.8);
    --text-muted: rgba(26, 26, 26, 0.6);
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: rgba(108, 91, 185, 0.05);
    --border-color: rgba(108, 91, 185, 0.2);
    --card-background: rgba(108, 91, 185, 0.05);
    --navbar-background: rgba(255, 255, 255, 0.95);
    --footer-background: #f1f3f4;
    --box-shadow: 0 10px 30px rgba(108, 91, 185, 0.15);
}

/* Smooth theme transitions */
.theme-transitioning,
.theme-transitioning * {
    transition: background-color 0.3s ease,
                color 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
}

/* ===== RTL LANGUAGE SUPPORT ===== */
/* RTL Base Styles */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] body {
    font-family: 'Tajawal', 'Cairo', 'Inter', sans-serif;
}

/* Arabic Typography */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    font-family: 'Cairo', 'Tajawal', 'Inter', sans-serif;
    font-weight: 600;
}

[dir="rtl"] .hero-title {
    font-family: 'Cairo', 'Tajawal', 'Inter', sans-serif;
    font-weight: 700;
    line-height: 1.2;
}

[dir="rtl"] .section-title {
    font-family: 'Cairo', 'Tajawal', 'Inter', sans-serif;
    font-weight: 600;
}

/* Arabic Text Improvements */
[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] a {
    font-family: 'Tajawal', 'Inter', sans-serif;
    line-height: 1.8;
}

[dir="rtl"] .btn {
    font-family: 'Tajawal', 'Inter', sans-serif;
    font-weight: 500;
}

/* RTL Navigation */
[dir="rtl"] .nav-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-link {
    margin-left: 0;
    margin-right: 2rem;
}

[dir="rtl"] .nav-link:last-child {
    margin-right: 0;
}

/* Language Toggle Styles */
.language-toggle {
    background: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    margin-left: 1rem;
    margin-right: 1rem;
}

.language-toggle:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.language-toggle i {
    font-size: 1rem;
}

.language-toggle span {
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

/* RTL Language Toggle */
[dir="rtl"] .language-toggle {
    margin-left: 1rem;
    margin-right: 1rem;
}

/* RTL Hero Section */
[dir="rtl"] .hero .container {
    text-align: right;
}

[dir="rtl"] .hero-content {
    text-align: right;
}

[dir="rtl"] .cta-buttons {
    justify-content: flex-end;
}

/* RTL Floating Elements */
[dir="rtl"] .floating-card {
    transform: translateX(20px);
}

[dir="rtl"] .floating-card:nth-child(even) {
    transform: translateX(-20px);
}

/* RTL Services Section */
[dir="rtl"] .service-card {
    text-align: right;
}

[dir="rtl"] .service-card .service-icon {
    margin-left: 0;
    margin-right: auto;
}

/* RTL Stats Section */
[dir="rtl"] .stat-item {
    text-align: right;
}

/* RTL Footer */
[dir="rtl"] .footer-content {
    text-align: right;
}

[dir="rtl"] .footer-section h3 {
    text-align: right;
}

[dir="rtl"] .footer-links {
    text-align: right;
}

[dir="rtl"] .footer-links a {
    text-align: right;
}

[dir="rtl"] .social-links {
    justify-content: flex-end;
}

/* RTL Mobile Menu */
[dir="rtl"] .hamburger {
    left: 20px;
    right: auto;
}

[dir="rtl"] .nav-menu.active {
    right: auto;
    left: 0;
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .nav-link {
        margin-right: 0;
        margin-bottom: 1rem;
        text-align: right;
    }

    [dir="rtl"] .hero-content {
        text-align: center;
    }

    [dir="rtl"] .cta-buttons {
        justify-content: center;
    }
}

/* RTL Animations */
[dir="rtl"] .slide-in-left {
    animation: slideInRight 0.6s ease-out;
}

[dir="rtl"] .slide-in-right {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* RTL Form Elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
}

[dir="rtl"] .form-group label {
    text-align: right;
}

/* RTL Button Icons */
[dir="rtl"] .btn i {
    margin-left: 8px;
    margin-right: 0;
}

[dir="rtl"] .btn i:first-child {
    margin-left: 0;
    margin-right: 8px;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 14px 28px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(108, 91, 185, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-secondary:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-outline:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== PRELOADER ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.preloader-content {
    text-align: center;
}

.logo-animation h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 3px;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(192, 165, 213, 0.2);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: transparent;
    backdrop-filter: none;
    z-index: 1000;
    transition: var(--transition);
    padding: 15px 0;
    border-bottom: none;
}

.navbar.scrolled {
    padding: 10px 0;
    background: transparent;
    box-shadow: none;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.client-area {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    padding: 8px 16px !important;
    border-radius: 20px;
    color: var(--white) !important;
}

.client-area::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

.theme-toggle {
    background: transparent;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    cursor: pointer;
    transition: var(--transition);
    margin-left: 15px;
}

.theme-toggle:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1rem;
    transition: var(--transition);
}
